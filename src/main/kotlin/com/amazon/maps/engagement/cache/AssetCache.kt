/*
 * Copyright 2025 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 */

package com.amazon.maps.engagement.cache

import android.net.Uri
import java.io.InputStream

/**
 * Interface for caching assets with encryption support.
 */
interface AssetCache {

    /**
     * Initializes the cache, must be called before any other methods
     */
    fun initialize()

    /**
     * Check if cache has been initialized
     */
    fun isInitialized(): Boolean

    /**
     * Return whether there is room in the cache for additional items
     * @return true if there is additional space left to save items
     */
    fun hasSpaceAvailable(): Bo<PERSON>an

    /**
     * Save the data from the provided InputStream
     * @param key Unique Id of the value, will overwrite previous cached file for same key
     * @param inputStream stream to use as source for the data
     * @param groupingDirectory a sub directory where asset will be saved (i.e. address Id)
     * @return the filePath of the cached data
     */
    fun save(
        key: String,
        inputStream: InputStream,
        groupingDirectory: String? = null
    ): String?

    /**
     * Returns Uri to cached file if it exists
     * @param key Unique Id of the value to retrieve
     * @return File url if key has cached value
     */
    fun get(key: String): String?

    /**
     * Removes cached file for provided key
     * @param key Unique Id of the value to remove
     */
    fun remove(key: String)

    /**
     * Removes all cached files
     * @param groupingDirectory a sub directory from where asset will be deleted (i.e. address Id)
     */
    fun removeAll(groupingDirectory: String? = null)

    /**
     * Total capacity - not guaranteed. Remaining FS space will impact available capacity
     */
    fun capacity(): Long

    /**
     * Current cache size
     */
    fun size(): Long

    /**
     * Get cache type
     */
    fun cacheType(): AssetCacheType

    /**
     * Checks if the given URI points to a file in this cache
     * @param uri The URI to check
     * @return true if the URI points to a file in this cache, false otherwise
     */
    fun isUriFromCache(uri: Uri): Boolean
}

/**
 * Types of asset caches supported
 */
enum class AssetCacheType {

    /**
     * Cache for map-related media assets
     */
    MAP_MEDIA,
}
