/*
 * Copyright 2024 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 */

package com.amazon.maps.engagement.cache

import android.content.Context
import android.net.Uri
import android.os.StatFs
import androidx.annotation.VisibleForTesting
import androidx.annotation.VisibleForTesting.PRIVATE
import com.amazon.majixplatform.log.logDebug
import com.amazon.majixplatform.log.logError
import com.amazon.majixplatform.log.logInfo
import com.amazon.maps.engagement.security.ImageEncryptionManager
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.util.concurrent.ConcurrentHashMap
import javax.inject.Inject

/**
 * Implementation of [AssetCache] that provides encrypted file caching functionality.
 */
internal class AssetCacheImpl @Inject constructor(
    private val context: Context,
    private val fileEncryptionManager: ImageEncryptionManager,
    internal val cacheType: AssetCacheType,
    @get:VisibleForTesting internal val cacheSize: Long = DEFAULT_SIZE
) : AssetCache {

    private var initialized: Boolean = false

    @get:VisibleForTesting(otherwise = PRIVATE)
    internal val cacheMap: ConcurrentHashMap<String, String> = ConcurrentHashMap()

    @get:VisibleForTesting(otherwise = PRIVATE)
    internal var storageManager = StorageManager()

    private var size: Long = 0
    private val directoryName = getDirectoryFromCacheType(cacheType)

    companion object {
        const val DEFAULT_SIZE: Long = 50 * 1024 * 1024
        const val TMP_FILE_EXT: String = ".jpeg"
        const val ENCRYPTED_FILE_EXT: String = "$TMP_FILE_EXT.${ImageEncryptionManager.FILE_EXTENSION}"
        const val CACHE_TYPE_MAP_MEDIA: String = "mapMediaCache"
    }

    override fun initialize() {
        logDebug { "AssetCache initialize" }

        storageManager.baseFilePath = baseFilePath()

        // determine total size of current cached items
        size = calculateCurrentCacheSize()

        initialized = true
    }

    override fun isInitialized(): Boolean {
        return initialized
    }

    override fun hasSpaceAvailable(): Boolean {
        if (!initialized) return false

        val fsSize = storageManager.availableFSSpace()
        logDebug { "Current Cache Size: $size, Capacity: $cacheSize, FS Space: $fsSize" }

        val maxSize: Long = cacheSize.coerceAtMost(fsSize)
        return size < maxSize
    }

    override fun save(
        key: String,
        inputStream: InputStream,
        groupingDirectory: String?
    ): String? {
        logDebug { "AssetCache save for key: $key" }

        if (!initialized) return null

        val existingFilePath = get(key)
        when {
            existingFilePath != null -> {
                val existingFile = storageManager.encryptedFileFromPath(existingFilePath)
                if (existingFile != null) {
                    size -= existingFile.length()
                    existingFile.delete()
                }
            }
        }

        val tmpFile = storageManager.saveTempFile(key, inputStream, groupingDirectory)

        var filePath: String? = null
        if (tmpFile.exists()) {
            // Encrypt the file and delete the original file.
            val success = fileEncryptionManager.encryptImage(tmpFile.absolutePath)
            tmpFile.delete()

            // Update cache status on successful encrypted file save
            if (success) {
                val previousSize = size
                val encryptedFile = storageManager.encryptedFileForKey(key, groupingDirectory)
                size += encryptedFile.length()
                cacheMap[key] = encryptedFile.absolutePath

                logDebug { "AssetCache cache size: previous -> $previousSize, current -> $size" }

                filePath = encryptedFile.absolutePath
            }
        }

        return filePath
    }

    override fun get(key: String): String? {
        logDebug { "AssetCache get key: $key" }
        if (!initialized) return null
        return cacheMap[key]
    }

    override fun remove(key: String) {
        logInfo { "AssetCache remove key: $key" }
        if (!initialized) return

        cacheMap[key]?.let {
            cacheMap.remove(key)
            val file = storageManager.encryptedFileFromPath(it)
            if (file != null) {
                size -= file.length().coerceAtMost(size)
                file.delete()
            }
        }
    }

    override fun removeAll(groupingDirectory: String?) {
        logDebug {
            "AssetCache remove all.\nAssetCache size before clean up: $size \nCacheMap size before cleanup: ${cacheMap.keys.size}"
        }

        if (!initialized) return

        val files = if (groupingDirectory == null) {
            storageManager.rootDirectory().listFiles()
        } else {
            storageManager.subDirectory(groupingDirectory).listFiles()
        }

        files?.forEach {
            remove(storageManager.keyFromFile(it))
        }

        logDebug { "AssetCache size after clean up: $size \nCacheMap size after cleanup: ${cacheMap.keys.size}" }

        if (groupingDirectory == null) {
            size = 0
            cacheMap.clear()
        } else {
            storageManager.subDirectory(groupingDirectory).delete()
        }
    }

    override fun capacity(): Long {
        return if (initialized) {
            cacheSize
        } else {
            0
        }
    }

    override fun size(): Long {
        return if (initialized) {
            size
        } else {
            0
        }
    }

    override fun cacheType(): AssetCacheType {
        return cacheType
    }

    /**
     * Checks if the given URI points to a file in this cache
     * @param uri The URI to check
     * @return true if the URI points to a file in this cache, false otherwise
     */
    override fun isUriFromCache(uri: Uri): Boolean {
        if (uri.scheme != "file") return false

        val path = uri.path ?: return false
        return path.contains(directoryName)
    }

    private fun getDirectoryFromCacheType(cacheType: AssetCacheType): String {
        return when (cacheType) {
            AssetCacheType.MAP_MEDIA -> CACHE_TYPE_MAP_MEDIA
        }
    }

    private fun baseFilePath(): String {
        val baseFilePath = "${context.filesDir.path}/$directoryName"
        // Create directory if that doesn't exists
        if (File(baseFilePath).exists().not()) {
            val result = File(baseFilePath).mkdir()
            if (result) {
                logDebug { "Created $directoryName directory successfully: $baseFilePath" }
            } else {
                logError { "Failed to create $directoryName directory: $baseFilePath" }
            }
        }
        return baseFilePath
    }

    @VisibleForTesting(otherwise = PRIVATE)
    internal fun getSubDirectoryFilePath(groupingKey: String): String {
        val subDirectoryFilePath = "${context.filesDir.path}/$directoryName/$groupingKey"
        if (File(subDirectoryFilePath).exists().not()) {
            val result = File(subDirectoryFilePath).mkdir()
            if (result) {
                logDebug { "Created $groupingKey sub-directory successfully: $subDirectoryFilePath" }
            } else {
                logError { "Failed to create $groupingKey sub-directory: $subDirectoryFilePath" }
            }
        }
        return subDirectoryFilePath
    }

    private fun calculateCurrentCacheSize(): Long {
        val files = storageManager.rootDirectory().listFiles()

        cacheMap.clear()
        var total: Long = 0
        files?.forEach { file ->
            // Ignore and cleanup files that aren't part of our cached content
            if (storageManager.isCacheFile(file)) {
                total += file.length()
                cacheMap[storageManager.keyFromFile(file)] = file.absolutePath
            } else {
                file.delete()
            }
        }

        logDebug { "AssetCache current cache size '$total' bytes" }
        return total
    }

    /**
     * Helper class handles interaction with filesystem. Easy to mock for Unit tests
     */
    @VisibleForTesting(otherwise = PRIVATE)
    open inner class StorageManager {
        lateinit var baseFilePath: String

        open fun rootDirectory(): File {
            return File(baseFilePath)
        }

        open fun subDirectory(subDirectory: String): File {
            return File(getSubDirectoryFilePath(subDirectory))
        }

        open fun isCacheFile(file: File): Boolean {
            return file.name.endsWith(ENCRYPTED_FILE_EXT)
        }

        open fun tmpFileForKey(
            key: String,
            subDirectory: String? = null
        ): File {
            return if (subDirectory == null) {
                File(baseFilePath, "$key$TMP_FILE_EXT")
            } else {
                File(getSubDirectoryFilePath(subDirectory), "$key$TMP_FILE_EXT")
            }
        }

        open fun encryptedFileForKey(
            key: String,
            subDirectory: String? = null
        ): File {
            return if (subDirectory == null) {
                File(baseFilePath, "$key$ENCRYPTED_FILE_EXT")
            } else {
                File(getSubDirectoryFilePath(subDirectory), "$key$ENCRYPTED_FILE_EXT")
            }
        }

        open fun encryptedFileFromPath(path: String): File? {
            val file = File(path)
            return if (file.exists()) {
                file
            } else {
                null
            }
        }

        open fun keyFromFile(file: File): String {
            return file.name.substringBefore('.')
        }

        open fun availableFSSpace(): Long {
            val stats = StatFs(baseFilePath)
            return stats.availableBlocksLong * stats.blockSizeLong
        }

        @Suppress("TooGenericExceptionCaught")
        open fun saveTempFile(
            key: String,
            inputStream: InputStream,
            subDirectory: String?
        ): File {
            // Save image to tmp file before encrypting
            val file = tmpFileForKey(key, subDirectory)
            var fileOutputStream: FileOutputStream? = null

            try {
                fileOutputStream = FileOutputStream(file)
                inputStream.copyTo(fileOutputStream)
            } catch (e: Exception) {
                logError(e) { "Error saving bitmap to AssetCache" }
            } finally {
                fileOutputStream?.flush()
                fileOutputStream?.close()
                inputStream.close()
            }

            return file
        }
    }
}
