package com.amazon.maps.engagement.security

import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import org.assertj.core.api.Assertions.assertThat
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import java.io.ByteArrayInputStream
import java.io.File
import java.io.IOException
import java.security.InvalidKeyException
import java.security.NoSuchAlgorithmException
import javax.crypto.Cipher
import javax.crypto.NoSuchPaddingException

@RunWith(RobolectricTestRunner::class)
class ImageEncryptionManagerTest {

    @MockK(relaxed = true)
    private lateinit var mockEncryptionKeyAPI: EncryptionKeyAPI

    @MockK(relaxed = true)
    private lateinit var mockAndroidKeyStoreKeyProvider: AndroidKeyStoreKeyProvider

    @MockK(relaxed = true)
    private lateinit var mockCipher: Cipher

    private lateinit var imageEncryptionManager: ImageEncryptionManager

    @Before
    fun setUp() {
        MockKAnnotations.init(this)

        // Mock the encryption key API initialization
        every { mockEncryptionKeyAPI.initialize() } returns Unit
        every { mockEncryptionKeyAPI.isKeyAvailable() } returns true
        every { mockEncryptionKeyAPI.refreshKey() } returns true

        // Mock the AndroidKeyStoreKeyProvider initialization
        every { mockAndroidKeyStoreKeyProvider.initialize() } returns Unit
        every { mockAndroidKeyStoreKeyProvider.isKeyAvailable() } returns true
        every { mockAndroidKeyStoreKeyProvider.refreshKey() } returns true

        imageEncryptionManager = ImageEncryptionManager(mockEncryptionKeyAPI)
    }

    @Test
    fun `initialization should call encryptionKeyAPI initialize`() {
        // Given
        val mockAPI = mockk<EncryptionKeyAPI>(relaxed = true)
        every { mockAPI.isKeyAvailable() } returns true

        // When
        ImageEncryptionManager(mockAPI)

        // Then
        verify { mockAPI.initialize() }
        verify { mockAPI.isKeyAvailable() }
    }

    @Test
    fun `initialization should refresh key when not available`() {
        // Given
        val mockAPI = mockk<EncryptionKeyAPI>(relaxed = true)
        every { mockAPI.isKeyAvailable() } returns false
        every { mockAPI.refreshKey() } returns true

        // When
        ImageEncryptionManager(mockAPI)

        // Then
        verify { mockAPI.initialize() }
        verify { mockAPI.isKeyAvailable() }
        verify { mockAPI.refreshKey() }
    }

    @Test
    fun `getCipher should return cipher from AndroidKeyStoreKeyProvider`() {
        // Given
        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } returns mockCipher
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        // When - using reflection to test private method
        val getCipherMethod = ImageEncryptionManager::class.java.getDeclaredMethod("getCipher", Int::class.java)
        getCipherMethod.isAccessible = true
        val result = getCipherMethod.invoke(manager, Cipher.ENCRYPT_MODE)

        // Then
        assertThat(result).isEqualTo(mockCipher)
    }

    @Test
    fun `getCipher should throw exception when encryptionKeyAPI is not AndroidKeyStoreKeyProvider`() {
        // Given
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI)

        // When - using reflection to test private method
        try {
            val getCipherMethod = ImageEncryptionManager::class.java.getDeclaredMethod("getCipher", Int::class.java)
            getCipherMethod.isAccessible = true
            getCipherMethod.invoke(manager, Cipher.ENCRYPT_MODE)

            // Should not reach here
            assertThat(false).isTrue()
        } catch (e: java.lang.reflect.InvocationTargetException) {
            // Expected - the actual exception is wrapped in InvocationTargetException
            assertThat(e.cause).isInstanceOf(IllegalStateException::class.java)
            assertThat(e.cause?.message).isEqualTo("Encryption key not available")
        } catch (e: IllegalStateException) {
            // Also acceptable - direct exception
            assertThat(e.message).isEqualTo("Encryption key not available")
        }
    }

    @Test
    fun `getEncryptedFile should append extension when not present`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI)

        // When - using reflection to test private method
        val getEncryptedFileMethod = ImageEncryptionManager::class.java.getDeclaredMethod("getEncryptedFile", String::class.java)
        getEncryptedFileMethod.isAccessible = true
        val result = getEncryptedFileMethod.invoke(manager, filePath) as File

        // Then
        assertThat(result.path).isEqualTo("/path/to/image.jpg.encrypted")
    }

    @Test
    fun `getEncryptedFile should not append extension when already present`() {
        // Given
        val filePath = "/path/to/image.jpg.encrypted"
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI)

        // When - using reflection to test private method
        val getEncryptedFileMethod = ImageEncryptionManager::class.java.getDeclaredMethod("getEncryptedFile", String::class.java)
        getEncryptedFileMethod.isAccessible = true
        val result = getEncryptedFileMethod.invoke(manager, filePath) as File

        // Then
        assertThat(result.path).isEqualTo("/path/to/image.jpg.encrypted")
    }

    @Test
    fun `encryptImage with filePath should throw FileNotFoundException for non-existent file`() {
        // Given
        val filePath = "/non/existent/path/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        // When & Then
        try {
            manager.encryptImage(filePath)
            // Should not reach here
            assertThat(false).isTrue()
        } catch (e: Exception) {
            // Expected - file doesn't exist
            assertThat(e).isInstanceOf(IOException::class.java)
        }
    }

    @SuppressWarnings("SwallowedException")
    @Test
    fun `encryptImage with InputStream should fail when cipher not available`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI) // Using non-AndroidKeyStore provider

        // When & Then
        try {
            manager.encryptImage(filePath, inputStream)
            // Should not reach here
            assertThat(false).isTrue()
        } catch (e: IllegalStateException) {
            // Expected - cipher not available
            assertThat(e.message).isEqualTo("Encryption key not available")
        } catch (e: Exception) {
            // Also acceptable - any exception due to cipher not being available
            assertThat(true).isTrue()
        }
    }

    @SuppressWarnings("SwallowedException")
    @Test
    fun `decryptImage should fail when cipher not available`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI) // Using non-AndroidKeyStore provider

        // When & Then
        try {
            manager.decryptImage(filePath)
            // Should not reach here
            assertThat(false).isTrue()
        } catch (e: IllegalStateException) {
            // Expected - cipher not available
            assertThat(e.message).isEqualTo("Encryption key not available")
        } catch (e: Exception) {
            // Also acceptable - any exception due to cipher not being available
            assertThat(true).isTrue()
        }
    }

    @SuppressWarnings("SwallowedException")
    @Test
    fun `decryptImageToByteArray should fail when cipher not available`() {
        // Given
        val filePath = "/path/to/image.jpg"
        val manager = ImageEncryptionManager(mockEncryptionKeyAPI) // Using non-AndroidKeyStore provider

        // When & Then
        try {
            manager.decryptImageToByteArray(filePath)
            // Should not reach here
            assertThat(false).isTrue()
        } catch (e: IllegalStateException) {
            // Expected - cipher not available
            assertThat(e.message).isEqualTo("Encryption key not available")
        } catch (e: Exception) {
            // Also acceptable - any exception due to cipher not being available
            assertThat(true).isTrue()
        }
    }

    @Test
    fun `FILE_EXTENSION constant should be encrypted`() {
        // Then
        assertThat(ImageEncryptionManager.FILE_EXTENSION).isEqualTo("encrypted")
    }

    @Test
    fun `encryptImage should throw InvalidKeyException when cipher initialization fails`() {
        // Given
        val filePath = "/tmp/test_image.jpg" // Use /tmp which should exist
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } throws InvalidKeyException("Invalid key")

        // When & Then
        try {
            manager.encryptImage(filePath, inputStream)
            assertThat(false).isTrue() // Should not reach here
        } catch (e: InvalidKeyException) {
            assertThat(e.message).isEqualTo("Invalid key")
        } catch (e: Exception) {
            // Also acceptable - the exception might be wrapped
            assertThat(e.cause).isInstanceOf(InvalidKeyException::class.java)
        }
    }

    @Test
    fun `encryptImage should throw NoSuchAlgorithmException when algorithm not available`() {
        // Given
        val filePath = "/tmp/test_image.jpg" // Use /tmp which should exist
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } throws NoSuchAlgorithmException("Algorithm not found")

        // When & Then
        try {
            manager.encryptImage(filePath, inputStream)
            assertThat(false).isTrue() // Should not reach here
        } catch (e: NoSuchAlgorithmException) {
            assertThat(e.message).isEqualTo("Algorithm not found")
        } catch (e: Exception) {
            // Also acceptable - the exception might be wrapped
            assertThat(e.cause).isInstanceOf(NoSuchAlgorithmException::class.java)
        }
    }

    @Test
    fun `encryptImage should throw NoSuchPaddingException when padding not available`() {
        // Given
        val filePath = "/tmp/test_image.jpg" // Use /tmp which should exist
        val inputStream = ByteArrayInputStream("test data".toByteArray())
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.ENCRYPT_MODE) } throws NoSuchPaddingException("Padding not found")

        // When & Then
        try {
            manager.encryptImage(filePath, inputStream)
            assertThat(false).isTrue() // Should not reach here
        } catch (e: NoSuchPaddingException) {
            assertThat(e.message).isEqualTo("Padding not found")
        } catch (e: Exception) {
            // Also acceptable - the exception might be wrapped
            assertThat(e.cause).isInstanceOf(NoSuchPaddingException::class.java)
        }
    }

    @Test
    fun `decryptImage should throw IOException when file not found`() {
        // Given
        val filePath = "/non/existent/path/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.DECRYPT_MODE) } returns mockCipher

        // When & Then
        try {
            manager.decryptImage(filePath)
            assertThat(false).isTrue() // Should not reach here
        } catch (e: Exception) {
            // Expected - file doesn't exist
            assertThat(e).isInstanceOf(IOException::class.java)
        }
    }

    @Test
    fun `decryptImageToByteArray should throw IOException when file not found`() {
        // Given
        val filePath = "/non/existent/path/image.jpg"
        val manager = ImageEncryptionManager(mockAndroidKeyStoreKeyProvider)

        every { mockAndroidKeyStoreKeyProvider.getCipher(Cipher.DECRYPT_MODE) } returns mockCipher

        // When & Then
        try {
            manager.decryptImageToByteArray(filePath)
            assertThat(false).isTrue() // Should not reach here
        } catch (e: Exception) {
            // Expected - file doesn't exist
            assertThat(e).isInstanceOf(IOException::class.java)
        }
    }
}
